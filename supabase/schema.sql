-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create profiles table (extends auth.users)
CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID REFERENCES auth.users(id) ON DELETE CASCADE PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    full_name TEXT,
    avatar_url TEXT,
    bio TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create shayaris table
CREATE TABLE IF NOT EXISTS public.shayaris (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    author TEXT NOT NULL,
    lines TEXT[] NOT NULL,
    tags TEXT[] DEFAULT '{}',
    likes_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    user_id UUID REFERENCES public.profiles(id) ON DELETE SET NULL,
    is_approved BOOLEAN DEFAULT true
);

-- Create likes table
CREATE TABLE IF NOT EXISTS public.likes (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    shayari_id UUID REFERENCES public.shayaris(id) ON DELETE CASCADE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(user_id, shayari_id)
);

-- Create comments table
CREATE TABLE IF NOT EXISTS public.comments (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    user_id UUID REFERENCES public.profiles(id) ON DELETE CASCADE NOT NULL,
    shayari_id UUID REFERENCES public.shayaris(id) ON DELETE CASCADE NOT NULL,
    content TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_shayaris_created_at ON public.shayaris(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_shayaris_likes_count ON public.shayaris(likes_count DESC);
CREATE INDEX IF NOT EXISTS idx_shayaris_tags ON public.shayaris USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_shayaris_user_id ON public.shayaris(user_id);
CREATE INDEX IF NOT EXISTS idx_likes_shayari_id ON public.likes(shayari_id);
CREATE INDEX IF NOT EXISTS idx_likes_user_id ON public.likes(user_id);
CREATE INDEX IF NOT EXISTS idx_comments_shayari_id ON public.comments(shayari_id);
CREATE INDEX IF NOT EXISTS idx_comments_user_id ON public.comments(user_id);

-- Function to update likes count
CREATE OR REPLACE FUNCTION update_likes_count()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.shayaris 
        SET likes_count = likes_count + 1 
        WHERE id = NEW.shayari_id;
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.shayaris 
        SET likes_count = likes_count - 1 
        WHERE id = OLD.shayari_id;
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Trigger to automatically update likes count
CREATE TRIGGER trigger_update_likes_count
    AFTER INSERT OR DELETE ON public.likes
    FOR EACH ROW EXECUTE FUNCTION update_likes_count();

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers to update updated_at
CREATE TRIGGER trigger_update_profiles_updated_at
    BEFORE UPDATE ON public.profiles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_shayaris_updated_at
    BEFORE UPDATE ON public.shayaris
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_comments_updated_at
    BEFORE UPDATE ON public.comments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.shayaris ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.likes ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.comments ENABLE ROW LEVEL SECURITY;

-- Profiles policies
CREATE POLICY "Public profiles are viewable by everyone" ON public.profiles
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own profile" ON public.profiles
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can update their own profile" ON public.profiles
    FOR UPDATE USING (auth.uid() = id);

-- Shayaris policies
CREATE POLICY "Approved shayaris are viewable by everyone" ON public.shayaris
    FOR SELECT USING (is_approved = true);

CREATE POLICY "Users can view their own shayaris" ON public.shayaris
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Authenticated users can insert shayaris" ON public.shayaris
    FOR INSERT WITH CHECK (auth.role() = 'authenticated');

CREATE POLICY "Users can update their own shayaris" ON public.shayaris
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own shayaris" ON public.shayaris
    FOR DELETE USING (auth.uid() = user_id);

-- Likes policies
CREATE POLICY "Likes are viewable by everyone" ON public.likes
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can insert likes" ON public.likes
    FOR INSERT WITH CHECK (auth.role() = 'authenticated' AND auth.uid() = user_id);

CREATE POLICY "Users can delete their own likes" ON public.likes
    FOR DELETE USING (auth.uid() = user_id);

-- Comments policies
CREATE POLICY "Comments are viewable by everyone" ON public.comments
    FOR SELECT USING (true);

CREATE POLICY "Authenticated users can insert comments" ON public.comments
    FOR INSERT WITH CHECK (auth.role() = 'authenticated' AND auth.uid() = user_id);

CREATE POLICY "Users can update their own comments" ON public.comments
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own comments" ON public.comments
    FOR DELETE USING (auth.uid() = user_id);

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.profiles (id, username, full_name, avatar_url)
    VALUES (
        NEW.id,
        COALESCE(NEW.raw_user_meta_data->>'username', split_part(NEW.email, '@', 1)),
        COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
        COALESCE(NEW.raw_user_meta_data->>'avatar_url', '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Insert some initial shayaris
INSERT INTO public.shayaris (title, author, lines, tags, likes_count) VALUES
('वक़्त का सफ़र', 'अज्ञात',
 ARRAY['वक़्त के पन्नों पर, यादों की स्याही है,', 'कुछ लम्हे कैद हैं, कुछ की गवाही है।', 'सफ़र ये ज़िन्दगी का, चलता ही जाएगा,', 'हर मोड़ पर कोई, अपना मिल जाएगा।'],
 ARRAY['ज़िन्दगी', 'वक़्त'], 258),

('दिल की आवाज़', 'कल्पना',
 ARRAY['खामोशियों में भी, एक आवाज़ होती है,', 'दिल की हर धड़कन, एक राज़ होती है।', 'समझ सको तो पढ़ लो, इन आँखों की गहराई,', 'हर नज़र में एक, पूरी किताब होती है।'],
 ARRAY['प्रेम', 'खामोशी'], 412),

('उम्मीद की किरण', 'साहिल',
 ARRAY['अंधेरों से न घबराना, ऐ दिल मेरे,', 'हर रात के बाद, सुबह होती है।', 'उम्मीद का दामन, थामे रखना,', 'मंज़िल उन्हीं को मिलती है, जिनमें जान होती है।'],
 ARRAY['प्रेरणा', 'उम्मीद'], 350),

('मोहब्बत का एहसास', 'रीता शर्मा',
 ARRAY['मोहब्बत में डूबे, ये दिल का हाल है,', 'हर सांस में बसा, तेरा ख्याल है।', 'इश्क़ की इस राह में, हमने पाया है,', 'जो खुशी मिली है, वो बेमिसाल है।'],
 ARRAY['प्रेम', 'इश्क़'], 389),

('जिंदगी के रंग', 'अमित कुमार',
 ARRAY['जिंदगी के रंग, कितने न्यारे हैं,', 'कभी खुशी, कभी गम के सहारे हैं।', 'हर दिन नया, एक नया अंदाज़ लेकर आता है,', 'जो गुज़र गया, वो यादों में समाता है।'],
 ARRAY['ज़िन्दगी', 'खुशी'], 298),

('दर्द भरी शायरी', 'विकास',
 ARRAY['दर्द की इस दास्तान में, क्या कहूं,', 'आंसुओं की भाषा में, क्या कहूं।', 'टूटे सपनों के टुकड़े, बिखरे पड़े हैं,', 'इन बिखरे ख्वाबों का, क्या कहूं।'],
 ARRAY['दर्द', 'गम'], 445),

('दोस्ती का प्यार', 'सुनीता',
 ARRAY['दोस्ती का ये रिश्ता, कितना प्यारा है,', 'हर मुश्किल में साथ, इसका सहारा है।', 'जिंदगी की राह में, जो मिले सच्चे दोस्त,', 'उनसे बढ़कर कोई, खजाना न्यारा है।'],
 ARRAY['दोस्ती', 'प्यार'], 367),

('सपनों की उड़ान', 'राज',
 ARRAY['सपनों की उड़ान में, हम खो जाते हैं,', 'नई मंजिलों की तलाश में, हम सो जाते हैं।', 'हौसलों के पंख लगाकर, आसमान छूना है,', 'मुश्किलों से लड़कर, अपना मुकाम पाना है।'],
 ARRAY['सपने', 'प्रेरणा'], 423);
