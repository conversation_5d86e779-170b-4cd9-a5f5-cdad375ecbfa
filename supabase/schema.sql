-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create shayaris table for blog posts
CREATE TABLE IF NOT EXISTS public.shay<PERSON> (
    id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
    title TEXT NOT NULL,
    author TEXT NOT NULL,
    lines TEXT[] NOT NULL,
    tags TEXT[] DEFAULT '{}',
    likes_count INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    is_published BOOLEAN DEFAULT true
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_shayaris_created_at ON public.shayaris(created_at DESC);
CREATE INDEX IF NOT EXISTS idx_shayaris_likes_count ON public.shayaris(likes_count DESC);
CREATE INDEX IF NOT EXISTS idx_shayaris_tags ON public.shayaris USING GIN(tags);
CREATE INDEX IF NOT EXISTS idx_shayaris_published ON public.shayaris(is_published);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update updated_at for shayaris
CREATE TRIGGER trigger_update_shayaris_updated_at
    BEFORE UPDATE ON public.shayaris
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) Policies for public read access

-- Enable RLS on shayaris table
ALTER TABLE public.shayaris ENABLE ROW LEVEL SECURITY;

-- Allow everyone to read published shayaris
CREATE POLICY "Published shayaris are viewable by everyone" ON public.shayaris
    FOR SELECT USING (is_published = true);

-- Insert some initial shayaris for the blog
INSERT INTO public.shayaris (title, author, lines, tags, likes_count, is_published) VALUES
('वक़्त का सफ़र', 'अज्ञात',
 ARRAY['वक़्त के पन्नों पर, यादों की स्याही है,', 'कुछ लम्हे कैद हैं, कुछ की गवाही है।', 'सफ़र ये ज़िन्दगी का, चलता ही जाएगा,', 'हर मोड़ पर कोई, अपना मिल जाएगा।'],
 ARRAY['ज़िन्दगी', 'वक़्त'], 258, true),

('दिल की आवाज़', 'कल्पना',
 ARRAY['खामोशियों में भी, एक आवाज़ होती है,', 'दिल की हर धड़कन, एक राज़ होती है।', 'समझ सको तो पढ़ लो, इन आँखों की गहराई,', 'हर नज़र में एक, पूरी किताब होती है।'],
 ARRAY['प्रेम', 'खामोशी'], 412, true),

('उम्मीद की किरण', 'साहिल',
 ARRAY['अंधेरों से न घबराना, ऐ दिल मेरे,', 'हर रात के बाद, सुबह होती है।', 'उम्मीद का दामन, थामे रखना,', 'मंज़िल उन्हीं को मिलती है, जिनमें जान होती है।'],
 ARRAY['प्रेरणा', 'उम्मीद'], 350, true),

('मोहब्बत का एहसास', 'रीता शर्मा',
 ARRAY['मोहब्बत में डूबे, ये दिल का हाल है,', 'हर सांस में बसा, तेरा ख्याल है।', 'इश्क़ की इस राह में, हमने पाया है,', 'जो खुशी मिली है, वो बेमिसाल है।'],
 ARRAY['प्रेम', 'इश्क़'], 389, true),

('जिंदगी के रंग', 'अमित कुमार',
 ARRAY['जिंदगी के रंग, कितने न्यारे हैं,', 'कभी खुशी, कभी गम के सहारे हैं।', 'हर दिन नया, एक नया अंदाज़ लेकर आता है,', 'जो गुज़र गया, वो यादों में समाता है।'],
 ARRAY['ज़िन्दगी', 'खुशी'], 298, true),

('दर्द भरी शायरी', 'विकास',
 ARRAY['दर्द की इस दास्तान में, क्या कहूं,', 'आंसुओं की भाषा में, क्या कहूं।', 'टूटे सपनों के टुकड़े, बिखरे पड़े हैं,', 'इन बिखरे ख्वाबों का, क्या कहूं।'],
 ARRAY['दर्द', 'गम'], 445, true),

('दोस्ती का प्यार', 'सुनीता',
 ARRAY['दोस्ती का ये रिश्ता, कितना प्यारा है,', 'हर मुश्किल में साथ, इसका सहारा है।', 'जिंदगी की राह में, जो मिले सच्चे दोस्त,', 'उनसे बढ़कर कोई, खजाना न्यारा है।'],
 ARRAY['दोस्ती', 'प्यार'], 367, true),

('सपनों की उड़ान', 'राज',
 ARRAY['सपनों की उड़ान में, हम खो जाते हैं,', 'नई मंजिलों की तलाश में, हम सो जाते हैं।', 'हौसलों के पंख लगाकर, आसमान छूना है,', 'मुश्किलों से लड़कर, अपना मुकाम पाना है।'],
 ARRAY['सपने', 'प्रेरणा'], 423, true);
