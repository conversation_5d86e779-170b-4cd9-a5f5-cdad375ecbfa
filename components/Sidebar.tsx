
import React, { useState, useEffect } from 'react';
import Widget from './Widget';
import HeartIcon from './icons/HeartIcon';
import { fetchPopularShayaris, getAllTags } from '../services/blogService';
import type { <PERSON><PERSON> } from '../types';

const Sidebar: React.FC = () => {
    const [popularShayaris, setPopularShayaris] = useState<Shayari[]>([]);
    const [tags, setTags] = useState<string[]>([]);
    const [isLoading, setIsLoading] = useState(true);

    useEffect(() => {
        const fetchSidebarData = async () => {
            try {
                const [popular, allTags] = await Promise.all([
                    fetchPopularShayaris(3),
                    getAllTags()
                ]);
                setPopularShayaris(popular);
                setTags(allTags.slice(0, 8)); // Show only first 8 tags
            } catch (error) {
                console.error('Error fetching sidebar data:', error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchSidebarData();
    }, []);

    return (
        <aside className="lg:w-1/3 flex-shrink-0 space-y-8">
            <Widget title="लोकप्रिय शायरी">
                <div className="space-y-4">
                    {isLoading ? (
                        Array.from({ length: 3 }).map((_, index) => (
                            <div key={index} className="animate-pulse">
                                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                                <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                            </div>
                        ))
                    ) : (
                        popularShayaris.map((item, index) => (
                            <div key={item.id || index} className="popular-item">
                                <h5 className="font-bold text-gray-700 font-hindi">{item.title}</h5>
                                <div className="flex justify-between items-center text-sm text-gray-500 mt-1">
                                    <p>~ {item.author}</p>
                                    <span className="flex items-center gap-1 text-red-500">
                                        {item.likes_count} <HeartIcon className="w-4 h-4" />
                                    </span>
                                </div>
                            </div>
                        ))
                    )}
                </div>
            </Widget>

            <Widget title="टैग्स">
                <div className="flex flex-wrap gap-2">
                    {isLoading ? (
                        Array.from({ length: 6 }).map((_, index) => (
                            <div key={index} className="h-6 bg-gray-200 rounded-lg w-16 animate-pulse"></div>
                        ))
                    ) : (
                        tags.map(tag => (
                            <span key={tag} className="bg-gray-200 text-gray-700 text-sm font-medium px-3 py-1.5 rounded-lg hover:bg-rose-200 hover:text-rose-800 transition-colors cursor-pointer">
                                #{tag}
                            </span>
                        ))
                    )}
                </div>
            </Widget>
            
            <Widget title="ब्लॉग के बारे में">
                <div className="flex flex-col items-center text-center">
                    <div className="w-24 h-24 rounded-full mb-4 bg-gradient-to-br from-rose-400 to-pink-500 flex items-center justify-center shadow-md">
                        <span className="text-white text-2xl font-bold">शा</span>
                    </div>
                    <h5 className="font-bold text-lg text-gray-800">हिंदी शायरी ब्लॉग</h5>
                    <p className="text-gray-600 text-sm mt-1">प्रेम, जिंदगी, और भावनाओं की सुंदर शायरी का संग्रह।</p>
                </div>
            </Widget>

            <Widget title="न्यूज़लेटर">
                <p className="text-gray-600 mb-4">रोज़ाना नई शायरी अपने इनबॉक्स में पाएं।</p>
                <div className="newsletter-signup flex flex-col gap-3">
                    <input type="email" className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-rose-400" placeholder="आपका ईमेल" />
                    <button className="w-full bg-rose-600 text-white font-bold py-2.5 px-4 rounded-lg hover:bg-rose-700 transition-colors">
                        सब्सक्राइब करें
                    </button>
                </div>
            </Widget>
        </aside>
    );
};

export default Sidebar;
