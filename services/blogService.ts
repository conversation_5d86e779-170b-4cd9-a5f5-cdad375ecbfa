import { supabase } from '../lib/supabase';
import type { <PERSON><PERSON> } from '../types';

export interface FetchShayarisOptions {
  limit?: number;
  offset?: number;
  orderBy?: 'created_at' | 'likes_count';
  orderDirection?: 'asc' | 'desc';
  tags?: string[];
}

export const fetchShayaris = async (options: FetchShayarisOptions = {}): Promise<Shayari[]> => {
  try {
    const {
      limit = 20,
      offset = 0,
      orderBy = 'created_at',
      orderDirection = 'desc',
      tags
    } = options;

    let query = supabase
      .from('shayaris')
      .select('*')
      .eq('is_published', true)
      .range(offset, offset + limit - 1)
      .order(orderBy, { ascending: orderDirection === 'asc' });

    // Filter by tags if provided
    if (tags && tags.length > 0) {
      query = query.overlaps('tags', tags);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching shayaris:', error);
      throw new Error('Failed to fetch shayaris');
    }

    return data || [];
  } catch (error) {
    console.error('Error in fetchShayaris:', error);
    throw error;
  }
};

export const fetchShayariById = async (id: string): Promise<Shayari | null> => {
  try {
    const { data, error } = await supabase
      .from('shayaris')
      .select('*')
      .eq('id', id)
      .eq('is_published', true)
      .single();

    if (error) {
      console.error('Error fetching shayari by ID:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in fetchShayariById:', error);
    return null;
  }
};

export const fetchShayarisByTag = async (tag: string, limit: number = 10): Promise<Shayari[]> => {
  try {
    const { data, error } = await supabase
      .from('shayaris')
      .select('*')
      .eq('is_published', true)
      .contains('tags', [tag])
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching shayaris by tag:', error);
      throw new Error('Failed to fetch shayaris by tag');
    }

    return data || [];
  } catch (error) {
    console.error('Error in fetchShayarisByTag:', error);
    throw error;
  }
};

export const fetchPopularShayaris = async (limit: number = 10): Promise<Shayari[]> => {
  try {
    const { data, error } = await supabase
      .from('shayaris')
      .select('*')
      .eq('is_published', true)
      .order('likes_count', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching popular shayaris:', error);
      throw new Error('Failed to fetch popular shayaris');
    }

    return data || [];
  } catch (error) {
    console.error('Error in fetchPopularShayaris:', error);
    throw error;
  }
};

export const fetchRecentShayaris = async (limit: number = 10): Promise<Shayari[]> => {
  try {
    const { data, error } = await supabase
      .from('shayaris')
      .select('*')
      .eq('is_published', true)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      console.error('Error fetching recent shayaris:', error);
      throw new Error('Failed to fetch recent shayaris');
    }

    return data || [];
  } catch (error) {
    console.error('Error in fetchRecentShayaris:', error);
    throw error;
  }
};

export const getTotalShayarisCount = async (): Promise<number> => {
  try {
    const { count, error } = await supabase
      .from('shayaris')
      .select('*', { count: 'exact', head: true })
      .eq('is_published', true);

    if (error) {
      console.error('Error getting total shayaris count:', error);
      return 0;
    }

    return count || 0;
  } catch (error) {
    console.error('Error in getTotalShayarisCount:', error);
    return 0;
  }
};

export const getAllTags = async (): Promise<string[]> => {
  try {
    const { data, error } = await supabase
      .from('shayaris')
      .select('tags')
      .eq('is_published', true);

    if (error) {
      console.error('Error fetching tags:', error);
      return [];
    }

    // Flatten and deduplicate tags
    const allTags = data?.flatMap(item => item.tags || []) || [];
    const uniqueTags = [...new Set(allTags)];
    
    return uniqueTags.sort();
  } catch (error) {
    console.error('Error in getAllTags:', error);
    return [];
  }
};
