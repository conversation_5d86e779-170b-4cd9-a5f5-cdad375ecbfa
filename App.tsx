
import React, { useState, useEffect } from 'react';
import Header from './components/Header';
import Hero from './components/Hero';
import ShayariCard from './components/ShayariCard';
import Sidebar from './components/Sidebar';
import Footer from './components/Footer';
import { fetchShayaris } from './services/blogService';
import type { <PERSON><PERSON> } from './types';

const App: React.FC = () => {
    const [shayaris, setShayaris] = useState<Shayari[]>([]);
    const [isLoading, setIsLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const [hasMore, setHasMore] = useState<boolean>(true);
    const [currentOffset, setCurrentOffset] = useState<number>(0);
    const ITEMS_PER_PAGE = 9;

    useEffect(() => {
        const fetchInitialData = async () => {
            try {
                setIsLoading(true);
                setError(null);
                const fetchedShayaris = await fetchShayaris({
                    limit: ITEMS_PER_PAGE,
                    offset: 0,
                    orderBy: 'created_at',
                    orderDirection: 'desc'
                });
                setShayaris(fetchedShayaris);
                setCurrentOffset(ITEMS_PER_PAGE);
                setHasMore(fetchedShayaris.length === ITEMS_PER_PAGE);
            } catch (err) {
                if (err instanceof Error) {
                    setError(err.message);
                } else {
                    setError("An unknown error occurred.");
                }
            } finally {
                setIsLoading(false);
            }
        };

        fetchInitialData();
    }, []);

    const loadMoreShayaris = async () => {
        try {
            const moreShayaris = await fetchShayaris({
                limit: ITEMS_PER_PAGE,
                offset: currentOffset,
                orderBy: 'created_at',
                orderDirection: 'desc'
            });

            if (moreShayaris.length > 0) {
                setShayaris(prev => [...prev, ...moreShayaris]);
                setCurrentOffset(prev => prev + ITEMS_PER_PAGE);
                setHasMore(moreShayaris.length === ITEMS_PER_PAGE);
            } else {
                setHasMore(false);
            }
        } catch (err) {
            console.error('Error loading more shayaris:', err);
        }
    };

    const LoadingSkeleton: React.FC = () => (
        <div className="bg-white rounded-xl shadow-lg animate-pulse">
            <div className="p-6">
                <div className="h-6 bg-gray-200 rounded w-3/4 mb-3"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-6"></div>
                <div className="space-y-3">
                    <div className="h-4 bg-gray-200 rounded"></div>
                    <div className="h-4 bg-gray-200 rounded w-5/6"></div>
                    <div className="h-4 bg-gray-200 rounded w-4/5"></div>
                </div>
            </div>
            <div className="px-6 pt-4 pb-6 bg-gray-50">
                 <div className="h-6 bg-gray-200 rounded w-1/2"></div>
            </div>
        </div>
    );

    return (
        <div className="bg-rose-50/25 min-h-screen text-gray-800">
            <Header />
            <Hero />

            <main className="container mx-auto px-4 py-12">
                <div className="flex flex-col lg:flex-row gap-12">
                    <div className="lg:w-2/3">
                        <h3 className="text-3xl font-bold mb-8 pb-2 border-b-2 border-rose-200 font-hindi">नवीनतम शायरी</h3>
                        {error && <p className="text-red-500 bg-red-100 p-4 rounded-lg">{`Error: ${error}. Displaying fallback content.`}</p>}
                        
                        <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
                            {isLoading ? (
                                Array.from({ length: 9 }).map((_, i) => <LoadingSkeleton key={i} />)
                            ) : (
                                shayaris.map((shayari, index) => (
                                    <ShayariCard key={shayari.id || index} shayari={shayari} />
                                ))
                            )}
                        </div>

                        {hasMore && !isLoading && (
                             <div className="text-center mt-12">
                                <button
                                    onClick={loadMoreShayaris}
                                    className="bg-white border-2 border-rose-600 text-rose-600 font-bold py-3 px-8 rounded-full hover:bg-rose-600 hover:text-white transition-all duration-300 shadow-md hover:shadow-lg"
                                >
                                    और शायरी लोड करें
                                </button>
                            </div>
                        )}
                    </div>
                    <Sidebar />
                </div>
            </main>
            
            <Footer />
        </div>
    );
};

export default App;
