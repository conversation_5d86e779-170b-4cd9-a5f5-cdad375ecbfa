import { createClient } from '@supabase/supabase-js';

const supabaseUrl = process.env.SUPABASE_URL || '';
const supabaseAnonKey = process.env.SUPABASE_ANON_KEY || '';

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables');
}

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

// Database types
export interface Database {
  public: {
    Tables: {
      shayaris: {
        Row: {
          id: string;
          title: string;
          author: string;
          lines: string[];
          tags: string[];
          likes_count: number;
          created_at: string;
          updated_at: string;
          user_id: string | null;
          is_approved: boolean;
        };
        Insert: {
          id?: string;
          title: string;
          author: string;
          lines: string[];
          tags: string[];
          likes_count?: number;
          created_at?: string;
          updated_at?: string;
          user_id?: string | null;
          is_approved?: boolean;
        };
        Update: {
          id?: string;
          title?: string;
          author?: string;
          lines?: string[];
          tags?: string[];
          likes_count?: number;
          created_at?: string;
          updated_at?: string;
          user_id?: string | null;
          is_approved?: boolean;
        };
      };
      profiles: {
        Row: {
          id: string;
          username: string;
          full_name: string | null;
          avatar_url: string | null;
          bio: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          username: string;
          full_name?: string | null;
          avatar_url?: string | null;
          bio?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          username?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          bio?: string | null;
          created_at?: string;
          updated_at?: string;
        };
      };
      likes: {
        Row: {
          id: string;
          user_id: string;
          shayari_id: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          shayari_id: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          shayari_id?: string;
          created_at?: string;
        };
      };
      comments: {
        Row: {
          id: string;
          user_id: string;
          shayari_id: string;
          content: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          shayari_id: string;
          content: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          shayari_id?: string;
          content?: string;
          created_at?: string;
          updated_at?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}
