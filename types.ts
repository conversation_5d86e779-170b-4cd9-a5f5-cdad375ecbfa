
export interface Shayari {
  id?: string;
  title: string;
  author: string;
  lines: string[];
  tags: string[];
  likes: number;
  likes_count?: number;
  created_at?: string;
  updated_at?: string;
  user_id?: string | null;
  is_approved?: boolean;
}

export interface User {
  id: string;
  email: string;
  username: string;
  full_name?: string;
  avatar_url?: string;
  bio?: string;
  created_at: string;
}

export interface Profile {
  id: string;
  username: string;
  full_name: string | null;
  avatar_url: string | null;
  bio: string | null;
  created_at: string;
  updated_at: string;
}

export interface Like {
  id: string;
  user_id: string;
  shayari_id: string;
  created_at: string;
}

export interface Comment {
  id: string;
  user_id: string;
  shayari_id: string;
  content: string;
  created_at: string;
  updated_at: string;
  user?: Profile;
}
